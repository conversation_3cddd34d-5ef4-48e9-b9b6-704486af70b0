#!/usr/bin/env python3
"""
Test script for the Beta GUI display
"""

import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from Beta_detection.beta_gui_display import show_beta_gui
    
    # Sample beta data for testing
    test_beta_results = {
        'ETH/USDT': {'beta': 1.786, 'correlation': 0.85, 'r_squared': 0.72},
        'SOL/USDT': {'beta': 1.449, 'correlation': 0.78, 'r_squared': 0.61},
        'DOGE/USDT': {'beta': 2.264, 'correlation': 0.65, 'r_squared': 0.42},
        'WIF/USDT': {'beta': 2.862, 'correlation': 0.72, 'r_squared': 0.52},
        'BNB/USDT': {'beta': 0.704, 'correlation': 0.68, 'r_squared': 0.46},
        'ADA/USDT': {'beta': 1.776, 'correlation': 0.81, 'r_squared': 0.66},
        'AVAX/USDT': {'beta': 1.978, 'correlation': 0.79, 'r_squared': 0.62},
        'LINK/USDT': {'beta': 1.873, 'correlation': 0.83, 'r_squared': 0.69},
        'TRX/USDT': {'beta': 0.314, 'correlation': 0.45, 'r_squared': 0.20},
        'XRP/USDT': {'beta': 1.127, 'correlation': 0.71, 'r_squared': 0.50}
    }
    
    # Sample previous betas for ROC calculation
    test_previous_betas = {
        'ETH/USDT': 1.650,
        'SOL/USDT': 1.520,
        'DOGE/USDT': 2.100,
        'WIF/USDT': 3.100,
        'BNB/USDT': 0.750,
        'ADA/USDT': 1.850,
        'AVAX/USDT': 1.800,
        'LINK/USDT': 1.950,
        'TRX/USDT': 0.280,
        'XRP/USDT': 1.200
    }
    
    print("🚀 Testing Beta GUI Display...")
    print("📊 Sample data loaded with 10 assets")
    print("🖥️  Opening GUI window...")
    
    # Show the GUI with test data
    show_beta_gui(test_beta_results, test_previous_betas, blocking=True)
    
    print("✅ GUI test completed!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure tkinter is installed: pip install tk")
    
except Exception as e:
    print(f"❌ Error testing GUI: {e}")
    import traceback
    traceback.print_exc()
