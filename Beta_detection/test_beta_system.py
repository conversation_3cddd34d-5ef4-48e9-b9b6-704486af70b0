#!/usr/bin/env python3
"""
Beta Detection System - Test Script
Quick test to verify the beta detection system is working correctly.
"""

import sys
import os
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Beta_detection.beta_runner import BetaDetectionRunner
from Beta_detection.beta_config_manager import BetaConfigManager
from Beta_detection.beta_calculator import BetaCalculator
from Beta_detection.beta_table_display import BetaTableDisplay

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_config_manager():
    """Test configuration manager functionality."""
    print("\n🔧 Testing Configuration Manager...")
    
    try:
        config = BetaConfigManager()
        
        # Test basic getters
        measurement_length = config.get_measurement_length()
        benchmark = config.get_benchmark_symbol()
        default_symbols = config.get_default_symbols()
        
        print(f"✅ Measurement Length: {measurement_length}")
        print(f"✅ Benchmark Symbol: {benchmark}")
        print(f"✅ Default Symbols: {len(default_symbols)} assets")
        
        # Test symbol groups
        defi_symbols = config.get_symbol_group('defi_focus')
        meme_symbols = config.get_symbol_group('meme_focus')
        
        print(f"✅ DeFi Group: {len(defi_symbols)} assets")
        print(f"✅ Meme Group: {len(meme_symbols)} assets")
        
        # Test validation
        issues = config.validate_config()
        if issues:
            print(f"⚠️  Configuration issues: {issues}")
        else:
            print("✅ Configuration validation passed")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_beta_calculator():
    """Test beta calculator with sample data."""
    print("\n🧮 Testing Beta Calculator...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample data
        dates = pd.date_range('2024-01-01', periods=120, freq='D')
        
        # BTC prices (benchmark)
        btc_prices = pd.Series(
            50000 + np.cumsum(np.random.randn(120) * 1000),
            index=dates,
            name='close'
        )
        
        # Asset prices (more volatile)
        asset_prices = pd.Series(
            100 + np.cumsum(np.random.randn(120) * 5),
            index=dates,
            name='close'
        )
        
        # Calculate beta
        calculator = BetaCalculator(measurement_length=100)
        beta = calculator.calculate_beta(asset_prices, btc_prices)
        
        print(f"✅ Sample Beta Calculation: {beta:.3f}")
        
        # Test return calculation
        returns = calculator.calculate_return_percent(btc_prices)
        print(f"✅ Return Calculation: {len(returns)} returns calculated")
        
        # Test ROC direction
        roc = calculator.calculate_beta_change_direction(1.5, 1.3)
        print(f"✅ ROC Direction: {roc}")
        
        return True
        
    except Exception as e:
        print(f"❌ Beta calculator test failed: {e}")
        return False

def test_display_system():
    """Test display system with sample data."""
    print("\n🖥️  Testing Display System...")
    
    try:
        # Create sample beta results
        sample_results = {
            'SOL/USDT': {'beta': 1.85, 'measurement_length': 100, 'data_points': 100, 'last_update': datetime.now().isoformat()},
            'ETH/USDT': {'beta': 1.42, 'measurement_length': 100, 'data_points': 100, 'last_update': datetime.now().isoformat()},
            'DOGE/USDT': {'beta': 2.15, 'measurement_length': 100, 'data_points': 100, 'last_update': datetime.now().isoformat()},
            'LINK/USDT': {'beta': 1.23, 'measurement_length': 100, 'data_points': 100, 'last_update': datetime.now().isoformat()},
            'XMR/USDT': {'beta': 0.87, 'measurement_length': 100, 'data_points': 100, 'last_update': datetime.now().isoformat()},
        }
        
        # Test display
        display = BetaTableDisplay()
        print("✅ Display system initialized")
        
        # Test table creation (this will print the table)
        display.create_beta_table(sample_results)
        
        # Test file saving
        filename = display.save_beta_results(sample_results, "test_beta_results.json")
        print(f"✅ Results saved to: {filename}")
        
        # Test loading previous results
        previous = display.load_previous_betas(filename)
        if previous:
            print(f"✅ Loaded {len(previous)} previous beta values")
        
        # Clean up test file
        try:
            os.remove(filename)
            print("✅ Test file cleaned up")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ Display system test failed: {e}")
        return False

def test_integration():
    """Test integration with a small subset of real data."""
    print("\n🔗 Testing System Integration...")
    
    try:
        # Test with a small subset of symbols
        test_symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']
        
        runner = BetaDetectionRunner()
        
        print(f"✅ Runner initialized")
        print(f"📊 Testing with symbols: {test_symbols}")
        
        # This would normally fetch real data and calculate betas
        # For testing, we'll just verify the runner can be initialized
        # and the configuration is loaded properly
        
        config = runner.config_manager
        measurement_length = config.get_measurement_length()
        benchmark = config.get_benchmark_symbol()
        
        print(f"✅ Configuration loaded: {measurement_length} days, benchmark: {benchmark}")
        
        # Test data fetching configuration
        data_config = config.get_data_fetching_config()
        print(f"✅ Data config: timeframe={data_config.get('timeframe')}, cache={data_config.get('use_cache')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Beta Detection System - Test Suite")
    print("="*60)
    
    tests = [
        ("Configuration Manager", test_config_manager),
        ("Beta Calculator", test_beta_calculator),
        ("Display System", test_display_system),
        ("System Integration", test_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name} Test...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "="*60)
    print("📋 TEST SUMMARY")
    print("="*60)
    
    passed = 0
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} - {test_name}")
        if success:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Beta detection system is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
