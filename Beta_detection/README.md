# Beta Detection System

A comprehensive cryptocurrency beta analysis system that calculates asset volatility relative to Bitcoin (BTC) benchmark. Based on TradingView implementation with enhanced features and configurability.

## 🎯 Overview

This system analyzes the beta coefficient of cryptocurrency assets against Bitcoin, helping identify:
- **High Beta Assets** (>1.5): More volatile than BTC
- **Medium Beta Assets** (1.0-1.5): Similar volatility to BTC  
- **Low Beta Assets** (<1.0): Less volatile than BTC

## 📁 System Components

```
Beta_detection/
├── beta_calculator.py          # Core beta calculation logic
├── beta_table_display.py       # Rich table display and formatting
├── beta_runner.py              # Main orchestration runner
├── beta_config_manager.py      # Configuration management
├── beta_config.yaml           # Configuration file
├── run_beta_analysis.py       # Command-line interface
├── beta_table_from_tradingview.txt  # Original TradingView reference
└── README.md                  # This file
```

## 🚀 Quick Start

### Basic Usage

```bash
# Run standard analysis with default assets 
python Beta_detection/run_beta_analysis.py

# Quick analysis for DeFi tokens
python Beta_detection/run_beta_analysis.py --group defi_focus --mode quick

# Analyze specific assets
python Beta_detection/run_beta_analysis.py --symbols SOL/USDT ETH/USDT DOGE/USDT

# Comprehensive analysis since specific date
python Beta_detection/run_beta_analysis.py --mode comprehensive --since 2024-01-01
```

### Show Configuration and Groups

```bash
# Show current configuration
python Beta_detection/run_beta_analysis.py --show-config

# List available symbol groups
python Beta_detection/run_beta_analysis.py --list-groups
```

## 📊 Analysis Modes

| Mode | Measurement Length | Buffer Days | Max Pages | Use Case |
|------|-------------------|-------------|-----------|----------|
| **quick** | 50 days | 15 days | 3 pages | Fast analysis |
| **standard** | 100 days | 30 days | 5 pages | Balanced analysis |
| **comprehensive** | 200 days | 60 days | 10 pages | Deep analysis |

## 🎯 Asset Groups

### All Binance Assets (175 total assets with 35+ days of data)
- **Major Cryptocurrencies**: BTC, ETH, SOL, XRP, BNB, ADA, AVAX, DOT, LTC, LINK
- **Layer 1 Blockchains**: SUI, APT, NEAR, ATOM, TRX, STX, SEI, INJ, TON, ICP, FIL, VET, THETA, EOS, NEO
- **Layer 2 & Scaling**: ARB, OP, POL, ZK, STRK, MANTA
- **DeFi Tokens**: AAVE, UNI, CRV, MKR, LDO, PENDLE, JUP, RAY, ORCA, GMX, ENS, COW, CAKE, RUNE, OSMO, CETUS
- **AI & Computing**: TAO, FET, WLD, ARKM, IO, CGPT, AIXBT
- **Meme Coins**: DOGE, SHIB, PEPE, WIF, BONK, FLOKI, BOME, NEIRO, PNUT, PENGU, 1MBABYDOGE, 1000SATS, 1000CAT
- **Gaming & Metaverse**: GALA, SAND, MANA, APE, YGG, TLM, CHZ, BIGTIME, PIXEL, BEAMX
- **Infrastructure**: PYTH, API3, TRB, HBAR, QNT, AR
- **Emerging Projects**: ENA, EIGEN, ETHFI, REZ, ONDO, JTO, W, ZRO, SAGA, OMNI, ALT, VANA, MOVE

### Specialized Groups
- **defi_focus**: 20 DeFi and lending protocols
- **meme_focus**: 20 meme coins and community tokens
- **layer1_focus**: 20 Layer 1 blockchain tokens
- **ai_computing_focus**: 8 AI and computing tokens
- **gaming_metaverse_focus**: 10 gaming and metaverse tokens
- **layer2_scaling_focus**: 6 Layer 2 scaling solutions
- **infrastructure_focus**: 6 infrastructure and oracle tokens
- **emerging_projects_focus**: 10 newest and emerging projects
- **top_50_by_volume**: 50 most liquid Binance assets

## 🔧 Configuration

Edit `Beta_detection/beta_config.yaml` to customize:

```yaml
beta_calculation:
  measurement_length: 100        # Days for beta calculation
  benchmark_symbol: "BTC/USDT"  # Benchmark (BTC only)
  roc_threshold: 0.05           # Threshold for significant change

display:
  max_assets_per_column: 15     # Split into dual columns if more
  show_statistics: true         # Show summary statistics
  save_results: true           # Save results to JSON
```

## 📈 Output Example

```
💎 Beta Table | BTC Benchmark | V1 💎
┏━━━━━━━━━━━━━┳━━━━━━━━━━━━┳━━━━━━━━━┓
┃ TICKER      ┃ Beta - BTC ┃ +/- ROC ┃
┡━━━━━━━━━━━━━╇━━━━━━━━━━━━╇━━━━━━━━━┩
│ PEPE/USDT   │       2.38 │    ↑    │
│ SOL/USDT    │       1.55 │    →    │
│ ETH/USDT    │       1.57 │    ↑    │
│ DOGE/USDT   │       1.79 │    ←    │
└─────────────┴────────────┴─────────┘

Beta Statistics
┏━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric              ┃   Value ┃
┡━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Assets        │      30 │
│ Average Beta        │   1.456 │
│ High Beta (>1.5)    │      12 │
│ Low Beta (≤1.0)     │       8 │
└─────────────────────┴─────────┘
```

## 🔍 Beta Interpretation

### Beta Values
- **β > 1.5**: High volatility relative to BTC (amplified moves)
- **β = 1.0**: Moves in sync with BTC
- **β < 1.0**: Lower volatility than BTC (defensive)
- **β < 0**: Moves opposite to BTC (rare)

### ROC Arrows
- **↑**: Beta increased significantly (>0.05)
- **↓**: Beta decreased significantly (>0.05)
- **→**: Beta increased slightly
- **←**: Beta decreased slightly

## 📁 File Outputs

Results are automatically saved to:
```
Beta_detection/beta_results_YYYYMMDD_HHMMSS.json
```

Contains:
- Timestamp and metadata
- Beta values for each asset
- Data quality metrics
- Measurement parameters

## 🛠️ Advanced Usage

### Custom Symbol Analysis
```python
from Beta_detection.beta_runner import BetaDetectionRunner

runner = BetaDetectionRunner()
results = runner.run_beta_analysis(
    symbols=['SOL/USDT', 'ETH/USDT', 'AVAX/USDT'],
    analysis_mode='comprehensive',
    since='2024-01-01'
)
```

### Configuration Management
```python
from Beta_detection.beta_config_manager import BetaConfigManager

config = BetaConfigManager()
config.update_config('beta_calculation', 'measurement_length', 150)
config.save_config()
```

## 📋 Command Line Options

```bash
python Beta_detection/run_beta_analysis.py [OPTIONS]

Options:
  --symbols SYMBOL [SYMBOL ...]    Specific symbols to analyze
  --group GROUP                    Use predefined symbol group
  --mode {quick,standard,comprehensive}  Analysis mode
  --since YYYY-MM-DD              Start date for analysis
  --length DAYS                   Beta measurement length
  --no-save                       Don't save results
  --no-comparison                 Don't show previous comparison
  --config PATH                   Custom configuration file
  --show-config                   Show configuration
  --list-groups                   List available groups
  -v, --verbose                   Verbose logging
```

## 🔄 Integration

The system integrates with the main Asset Screener through:
- **Unified Data Fetcher**: Uses existing data infrastructure
- **Configuration System**: Follows project patterns
- **Caching**: Leverages existing cache mechanisms
- **Logging**: Uses project logging standards

## 📊 Data Requirements

- **Minimum Data**: 80 candles for valid beta calculation
- **Recommended**: 100+ daily candles
- **Data Source**: Binance, GeckoTerminal (via unified fetcher)
- **Timeframe**: Daily (1d) candles only

## 🚨 Limitations

- **BTC Benchmark Only**: As requested, only BTC benchmark (no ETH)
- **Daily Timeframe**: Only daily candles supported
- **30-40 Assets**: Optimized for TradingView asset count
- **USD Pairs**: Primarily USDT pairs

## 🔮 Future Enhancements

- Real-time beta monitoring
- Alert system for significant beta changes
- Integration with main strategy system
- Historical beta trend analysis
- Portfolio beta calculation

## 📞 Support

For issues or questions:
1. Check configuration with `--show-config`
2. Use verbose mode with `-v` for debugging
3. Verify asset symbols with `--list-groups`
4. Check data availability in cache
