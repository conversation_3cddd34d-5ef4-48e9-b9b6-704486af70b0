2025-07-24 00:02:46,511 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 284 points
2025-07-24 00:02:46,512 - [BITVAVO] - root - INFO - DOGE/USDT B&H total return: -5.71%
2025-07-24 00:02:46,514 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 284 points
2025-07-24 00:02:46,515 - [BITVAVO] - root - INFO - BNB/USDT B&H total return: 25.55%
2025-07-24 00:02:46,517 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 284 points
2025-07-24 00:02:46,518 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -15.11%
2025-07-24 00:02:46,519 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-24 00:02:46,531 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-24 00:02:46,643 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-24 00:02:46,665 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-24 00:02:48,833 - [BITVAVO] - root - INFO - Added ETH/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,833 - [BITVAVO] - root - INFO - Added BTC/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,833 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,833 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,833 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,833 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,833 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO -   - ETH/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO -   - BTC/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO -   - SOL/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO -   - SUI/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,834 - [BITVAVO] - root - INFO -   - XRP/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - AAVE/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - AVAX/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - ADA/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - LINK/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - TRX/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - PEPE/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - DOGE/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - BNB/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,835 - [BITVAVO] - root - INFO -   - DOT/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,862 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-24 00:02:48,863 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-24 00:02:48,865 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-24 00:02:48,865 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-24 00:02:48,880 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-24 00:02:48,880 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-24 00:02:48,880 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-24 00:02:48,880 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-24 00:02:48,881 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-24 00:02:48,881 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-24 00:02:48,907 - [BITVAVO] - root - INFO - Loaded 2165 rows of BTC/USDT data from cache (last updated: 2025-07-24)
2025-07-24 00:02:48,909 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-24
2025-07-24 00:02:48,909 - [BITVAVO] - root - INFO - Loaded 2165 rows of BTC/USDT data from cache (after filtering).
2025-07-24 00:02:48,909 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-24 00:02:48,909 - [BITVAVO] - root - INFO - Fetched BTC data: 2165 candles from 2019-08-20 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:48,909 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-24 00:02:49,348 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1135)}
2025-07-24 00:02:49,348 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-24 00:02:49,349 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-24 00:02:49,461 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1139)}
2025-07-24 00:02:49,461 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-24 00:02:49,800 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:02:54,857 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-24 00:02:54,857 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-24 00:02:55,477 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-24 00:02:55,477 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(896)}
2025-07-24 00:02:55,478 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-24 00:02:55,478 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-24 00:02:57,195 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-24 00:02:57,196 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(991)}
2025-07-24 00:02:57,196 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-24 00:02:57,196 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-24 00:02:57,298 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-24 00:02:57,298 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-24 00:02:57,298 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-24 00:02:58,115 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-24 00:02:58,116 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-24 00:02:58,116 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-24 00:02:59,558 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-24 00:02:59,558 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-24 00:02:59,558 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-24 00:02:59,558 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-24 00:02:59,559 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-24 00:02:59,562 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-24 00:02:59,563 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 3.0}
2025-07-24 00:02:59,563 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 3.0}
2025-07-24 00:02:59,563 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-24 00:02:59,563 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-24 00:02:59,563 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 12.0)
2025-07-24 00:02:59,563 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 0.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 5.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 5.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 11.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 0.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 7.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 10.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 9.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 5.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 13.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 3.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 3.0)
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:02:59,564 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:02:59,564 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:02:59,581 - [BITVAVO] - root - INFO - Appended metrics to existing file: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250717_run_20250721_225444.csv
2025-07-24 00:02:59,581 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250717_run_20250721_225444.csv
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 284 entries
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 284 entries
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 284 entries
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 284 entries
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-24 00:02:59,582 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-24 00:02:59,583 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-24 00:02:59,583 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-24 00:02:59,585 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-24 00:02:59,585 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: XRP/EUR, DOGE/EUR
2025-07-24 00:02:59,586 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-19 00:00:00+00:00     ETH/EUR, XRP/EUR
2025-07-20 00:00:00+00:00     ETH/EUR, XRP/EUR
2025-07-21 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-22 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-23 00:00:00+00:00    XRP/EUR, DOGE/EUR
dtype: object
2025-07-24 00:02:59,586 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:02:59,586 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:02:59,586 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-24 00:02:59,586 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-24 00:02:59,586 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:02:59,587 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:02:59,587 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-24 00:02:59,587 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['DOGE/EUR']
2025-07-24 00:02:59,587 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: DOGE/EUR
2025-07-24 00:02:59,587 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: DOGE/EUR (score: 13.0)
2025-07-24 00:02:59,587 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: DOGE/EUR (MTPI signal: 1)
2025-07-24 00:02:59,587 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-24 00:02:59,587 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['DOGE/EUR']
2025-07-24 00:02:59,587 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: XRP/EUR, DOGE/EUR -> DOGE/EUR
2025-07-24 00:02:59,587 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: DOGE/EUR
2025-07-24 00:02:59,613 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-24 00:02:59,613 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:02:59,613 - [BITVAVO] - root - INFO - Available assets from config: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - Asset columns found in dataframe: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - Assets with non-zero allocation: ['XRP/EUR', 'DOGE/EUR']
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - All assets sorted by score: [('DOGE/EUR', 13.0), ('ETH/EUR', 12.0), ('XRP/EUR', 11.0), ('ADA/EUR', 10.0), ('LINK/EUR', 9.0), ('AVAX/EUR', 7.0), ('SOL/EUR', 5.0), ('SUI/EUR', 5.0), ('PEPE/EUR', 5.0), ('BNB/EUR', 3.0), ('DOT/EUR', 3.0), ('TRX/EUR', 1.0), ('BTC/EUR', 0.0), ('AAVE/EUR', 0.0)]
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - Selected top 2 assets by score: ['DOGE/EUR', 'ETH/EUR']
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - Updated assets list with top-scoring assets: ['DOGE/EUR', 'ETH/EUR']
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - Using weighted allocation with configured weights: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: DOGE/EUR
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-24 00:02:59,614 - [BITVAVO] - root - ERROR - 🚨 ? DOGE/EUR WAS SELECTED
2025-07-24 00:02:59,614 - [BITVAVO] - root - INFO - Executing multi-asset strategy with 2 assets: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:02:59,615 - [BITVAVO] - root - INFO - Passing 14 asset scores to executor for replacement logic
2025-07-24 00:02:59,699 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Starting get_current_price
2025-07-24 00:02:59,699 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Exchange ID: bitvavo
2025-07-24 00:02:59,699 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Using existing exchange instance
2025-07-24 00:02:59,699 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Symbol found in exchange markets
2025-07-24 00:02:59,700 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Attempting to fetch ticker...
2025-07-24 00:02:59,751 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker fetched successfully
2025-07-24 00:02:59,751 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker data: {'symbol': 'DOGE/EUR', 'timestamp': 1753315376061, 'datetime': '2025-07-24T00:02:56.061Z', 'high': 0.23059, 'low': 0.19602, 'bid': 0.204, 'bidVolume': 5660.45243789, 'ask': 0.20407, 'askVolume': 4158.0, 'vwap': 0.21066892186848266, 'open': 0.22929, 'close': 0.20423, 'last': 0.20423, 'previousClose': None, 'change': -0.02506, 'percentage': -10.929390727899166, 'average': 0.21676, 'baseVolume': 34151253.43909801, 'quoteVolume': 7194607.742472088, 'info': {'market': 'DOGE-EUR', 'startTimestamp': '1753228976061', 'timestamp': '1753315376061', 'open': '0.22929', 'openTimestamp': '1753229021404', 'high': '0.23059', 'low': '0.19602', 'last': '0.20423', 'closeTimestamp': '1753315332281', 'bid': '0.2040000', 'bidSize': '5660.45243789', 'ask': '0.2040700', 'askSize': '4158', 'volume': '34151253.43909801', 'volumeQuote': '7194607.742472087866'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:02:59,751 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Last price: 0.20423
2025-07-24 00:02:59,751 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-24 00:02:59,751 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-24 00:02:59,752 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-24 00:02:59,752 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-24 00:02:59,752 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-24 00:02:59,810 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-24 00:02:59,811 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753315376061, 'datetime': '2025-07-24T00:02:56.061Z', 'high': 3208.1, 'low': 2996.1, 'bid': 3077.7, 'bidVolume': 3.64972278, 'ask': 3078.7, 'askVolume': 3.51892571, 'vwap': 3081.6425855398325, 'open': 3187.4, 'close': 3078.9, 'last': 3078.9, 'previousClose': None, 'change': -108.5, 'percentage': -3.404028361674092, 'average': 3133.15, 'baseVolume': 18068.60996622, 'quoteVolume': 55680997.933412984, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753228976061', 'timestamp': '1753315376061', 'open': '3187.4', 'openTimestamp': '1753228985380', 'high': '3208.1', 'low': '2996.1', 'last': '3078.9', 'closeTimestamp': '1753315369599', 'bid': '3077.700', 'bidSize': '3.64972278', 'ask': '3078.700', 'askSize': '3.51892571', 'volume': '18068.60996622', 'volumeQuote': '55680997.933412987'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:02:59,811 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3078.9
2025-07-24 00:02:59,811 - [BITVAVO] - root - INFO - Original assets with weights: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:02:59,811 - [BITVAVO] - root - INFO - Using provided asset scores for replacement logic: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:02:59,811 - [BITVAVO] - root - INFO - Top assets by score:
2025-07-24 00:02:59,811 - [BITVAVO] - root - INFO -   DOGE/EUR: score=13.0, daily trade limit: OK
2025-07-24 00:02:59,811 - [BITVAVO] - root - INFO -   ETH/EUR: score=12.0, daily trade limit: OK
2025-07-24 00:02:59,811 - [BITVAVO] - root - INFO -   XRP/EUR: score=11.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO -   ADA/EUR: score=10.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO -   LINK/EUR: score=9.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO -   AVAX/EUR: score=7.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO -   SOL/EUR: score=5.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO -   SUI/EUR: score=5.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO -   PEPE/EUR: score=5.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO -   BNB/EUR: score=3.0, daily trade limit: OK
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO - Incremented daily trade counter for ETH/EUR: 1/5
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO - No assets were rejected during execution
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO - Attempting to exit position for XRP/EUR in live mode
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO - [DEBUG] PRICE - XRP/EUR: Starting get_current_price
2025-07-24 00:02:59,812 - [BITVAVO] - root - INFO - [DEBUG] PRICE - XRP/EUR: Exchange ID: bitvavo
2025-07-24 00:02:59,813 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Using existing exchange instance
2025-07-24 00:02:59,813 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Symbol found in exchange markets
2025-07-24 00:02:59,813 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Attempting to fetch ticker...
2025-07-24 00:02:59,887 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Ticker fetched successfully
2025-07-24 00:02:59,888 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Ticker data: {'symbol': 'XRP/EUR', 'timestamp': 1753315375761, 'datetime': '2025-07-24T00:02:55.761Z', 'high': 3.022, 'low': 2.589, 'bid': 2.6977, 'bidVolume': 1651.2, 'ask': 2.6983, 'askVolume': 1391.376, 'vwap': 2.7816874199870467, 'open': 3.015, 'close': 2.6979, 'last': 2.6979, 'previousClose': None, 'change': -0.3171, 'percentage': -10.517412935323383, 'average': 2.85645, 'baseVolume': 54063801.183129, 'quoteVolume': 150388595.62779075, 'info': {'market': 'XRP-EUR', 'startTimestamp': '1753228975761', 'timestamp': '1753315375761', 'open': '3.015', 'openTimestamp': '1753228982725', 'high': '3.022', 'low': '2.589', 'last': '2.6979', 'closeTimestamp': '1753315372546', 'bid': '2.697700', 'bidSize': '1651.200000', 'ask': '2.698300', 'askSize': '1391.376000', 'volume': '54063801.183129', 'volumeQuote': '150388595.6277907596'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:02:59,889 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Last price: 2.6979
2025-07-24 00:03:00,039 - [BITVAVO] - root - INFO - Current position for XRP/EUR: 0.00000000 units (entry price: 0.00000000)
2025-07-24 00:03:00,039 - [BITVAVO] - root - ERROR - No position found for XRP/EUR
2025-07-24 00:03:00,041 - [BITVAVO] - root - ERROR - Trade failed: SELL XRP/EUR, amount=0.00000000, price=2.69790000, reason=No position found for XRP/EUR
2025-07-24 00:03:00,041 - [BITVAVO] - root - ERROR - Failed to exit position for XRP/EUR: No position found for XRP/EUR
2025-07-24 00:03:00,090 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:00,702 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Starting get_current_price
2025-07-24 00:03:00,702 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Exchange ID: bitvavo
2025-07-24 00:03:00,702 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Using existing exchange instance
2025-07-24 00:03:00,703 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Symbol found in exchange markets
2025-07-24 00:03:00,703 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Attempting to fetch ticker...
2025-07-24 00:03:00,754 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker fetched successfully
2025-07-24 00:03:00,755 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker data: {'symbol': 'DOGE/EUR', 'timestamp': 1753315376061, 'datetime': '2025-07-24T00:02:56.061Z', 'high': 0.23059, 'low': 0.19602, 'bid': 0.204, 'bidVolume': 5660.45243789, 'ask': 0.20407, 'askVolume': 4158.0, 'vwap': 0.21066892186848266, 'open': 0.22929, 'close': 0.20423, 'last': 0.20423, 'previousClose': None, 'change': -0.02506, 'percentage': -10.929390727899166, 'average': 0.21676, 'baseVolume': 34151253.43909801, 'quoteVolume': 7194607.742472088, 'info': {'market': 'DOGE-EUR', 'startTimestamp': '1753228976061', 'timestamp': '1753315376061', 'open': '0.22929', 'openTimestamp': '1753229021404', 'high': '0.23059', 'low': '0.19602', 'last': '0.20423', 'closeTimestamp': '1753315332281', 'bid': '0.2040000', 'bidSize': '5660.45243789', 'ask': '0.2040700', 'askSize': '4158', 'volume': '34151253.43909801', 'volumeQuote': '7194607.742472087866'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:00,755 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Last price: 0.20423
2025-07-24 00:03:00,755 - [BITVAVO] - root - INFO - Total portfolio value (cached): 8019.43739512 EUR
2025-07-24 00:03:00,755 - [BITVAVO] - root - INFO - Using safe available balance: 4095.54040500 (99.99% of 4095.95000000)
2025-07-24 00:03:00,755 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-24 00:03:00,755 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-24 00:03:00,755 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-24 00:03:00,755 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-24 00:03:00,755 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-24 00:03:00,810 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-24 00:03:00,811 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753315376061, 'datetime': '2025-07-24T00:02:56.061Z', 'high': 3208.1, 'low': 2996.1, 'bid': 3077.7, 'bidVolume': 3.64972278, 'ask': 3078.7, 'askVolume': 3.51892571, 'vwap': 3081.6425855398325, 'open': 3187.4, 'close': 3078.9, 'last': 3078.9, 'previousClose': None, 'change': -108.5, 'percentage': -3.404028361674092, 'average': 3133.15, 'baseVolume': 18068.60996622, 'quoteVolume': 55680997.933412984, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753228976061', 'timestamp': '1753315376061', 'open': '3187.4', 'openTimestamp': '1753228985380', 'high': '3208.1', 'low': '2996.1', 'last': '3078.9', 'closeTimestamp': '1753315369599', 'bid': '3077.700', 'bidSize': '3.64972278', 'ask': '3078.700', 'askSize': '3.51892571', 'volume': '18068.60996622', 'volumeQuote': '55680997.933412987'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:00,811 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3078.9
2025-07-24 00:03:00,811 - [BITVAVO] - root - INFO - Asset ETH/EUR: weight=0.5000, price=3078.90000000, required=2042.62517987, amount=0.66177250
2025-07-24 00:03:00,811 - [BITVAVO] - root - INFO - Final assets to buy: {'ETH/EUR'}
2025-07-24 00:03:00,811 - [BITVAVO] - root - INFO - Weight for DOGE/EUR unchanged (current: 0.50, new: 0.50). Skipping adjustment.
2025-07-24 00:03:00,811 - [BITVAVO] - root - INFO - Processing position reductions (sells) first to free up capital...
2025-07-24 00:03:01,009 - [BITVAVO] - root - INFO - Available balance after position reductions: 4095.95000000 EUR
2025-07-24 00:03:01,009 - [BITVAVO] - root - INFO - Processing position increases (buys) after capital has been freed...
2025-07-24 00:03:01,010 - [BITVAVO] - root - INFO - Total sale proceeds to distribute: 0.00000000 EUR
2025-07-24 00:03:01,010 - [BITVAVO] - root - INFO - Total weight of new assets: 0.5000
2025-07-24 00:03:01,010 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-24 00:03:01,010 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-24 00:03:01,010 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-24 00:03:01,010 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-24 00:03:01,010 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-24 00:03:01,061 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-24 00:03:01,062 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753315376061, 'datetime': '2025-07-24T00:02:56.061Z', 'high': 3208.1, 'low': 2996.1, 'bid': 3077.7, 'bidVolume': 3.64972278, 'ask': 3078.7, 'askVolume': 3.51892571, 'vwap': 3081.6425855398325, 'open': 3187.4, 'close': 3078.9, 'last': 3078.9, 'previousClose': None, 'change': -108.5, 'percentage': -3.404028361674092, 'average': 3133.15, 'baseVolume': 18068.60996622, 'quoteVolume': 55680997.933412984, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753228976061', 'timestamp': '1753315376061', 'open': '3187.4', 'openTimestamp': '1753228985380', 'high': '3208.1', 'low': '2996.1', 'last': '3078.9', 'closeTimestamp': '1753315369599', 'bid': '3077.700', 'bidSize': '3.64972278', 'ask': '3078.700', 'askSize': '3.51892571', 'volume': '18068.60996622', 'volumeQuote': '55680997.933412987'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:01,062 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3078.9
2025-07-24 00:03:01,062 - [BITVAVO] - root - INFO - Using weight-based allocation for ETH/EUR: 2047.77020250 EUR (50.0% of 4095.54040500)
2025-07-24 00:03:01,062 - [BITVAVO] - root - INFO - Buying ETH/EUR: 0.66177250 units at 3078.90000000 (value: 2037.53135149)
2025-07-24 00:03:01,112 - [BITVAVO] - root - INFO - Adjusted base amount for ETH/EUR: 0.66177250 -> 0.65846364
2025-07-24 00:03:01,227 - [BITVAVO] - root - INFO - Created market buy order: ETH/EUR, amount: 0.6617725004019291, avg price: 3078.9
2025-07-24 00:03:01,227 - [BITVAVO] - root - INFO - Order fee: 5.076329593 EUR
2025-07-24 00:03:01,227 - [BITVAVO] - root - INFO - Filled amount: 0.65846363
2025-07-24 00:03:01,227 - [BITVAVO] - root - INFO - Entered position for ETH/EUR: 0.661773 units ($2047.77)
2025-07-24 00:03:01,227 - [BITVAVO] - root - INFO - Updated portfolio with successful trades: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:03:01,227 - [BITVAVO] - root - WARNING - Critical portfolio rebalancing failure detected: 33.3% success rate
2025-07-24 00:03:01,321 - [BITVAVO] - root - ERROR - Trade failed: UNKNOWN XRP/EUR, amount=0.00000000, price=0.00000000, reason=No position found for XRP/EUR
2025-07-24 00:03:01,326 - [BITVAVO] - root - INFO - Trade executed: BUY ETH/EUR, amount=0.66177250, price=3078.90000000, filled=0.65846363
2025-07-24 00:03:01,326 - [BITVAVO] - root - INFO -   Fee: 5.07632959 EUR
2025-07-24 00:03:01,326 - [BITVAVO] - root - INFO - Multi-asset trade result logged to trade log file
2025-07-24 00:03:01,327 - [BITVAVO] - root - INFO - Multi-asset trades partially executed: 1 of 2 trades successful
2025-07-24 00:03:01,423 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-24 00:03:01,427 - [BITVAVO] - root - WARNING - Some trades failed: 1 trades failed
2025-07-24 00:03:01,427 - [BITVAVO] - root - WARNING - Failed trade for XRP/EUR: No position found for XRP/EUR
2025-07-24 00:03:01,431 - [BITVAVO] - root - INFO - Asset selection logged: 2 assets selected with weighted_custom allocation
2025-07-24 00:03:01,431 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-24 00:03:01,431 - [BITVAVO] - root - INFO -   DOGE/EUR: score=13.0, status=SELECTED, weight=0.50
2025-07-24 00:03:01,431 - [BITVAVO] - root - INFO -   ETH/EUR: score=12.0, status=SELECTED, weight=0.50
2025-07-24 00:03:01,431 - [BITVAVO] - root - INFO -   XRP/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,431 - [BITVAVO] - root - INFO -   ADA/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   LINK/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   AVAX/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   SOL/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   SUI/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   PEPE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   BNB/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   DOT/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   BTC/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,432 - [BITVAVO] - root - INFO -   AAVE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:01,433 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 2 assets selected
2025-07-24 00:03:01,433 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:03:01,433 - [BITVAVO] - root - INFO - Top 2 assets by score: ['DOGE/EUR', 'ETH/EUR']
2025-07-24 00:03:01,575 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-24 00:03:01,578 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 55.28 seconds
2025-07-24 00:03:01,583 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-24 00:03:01,584 - [BITVAVO] - root - WARNING - Recovery from strategy_execution_failure failure was unsuccessful
2025-07-24 00:03:01,700 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-24 00:03:01,704 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:10,358 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:20,622 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:30,887 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:41,144 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:51,405 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:01,669 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:11,925 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:22,187 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:32,448 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:42,707 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:52,971 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:03,232 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:13,497 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:23,758 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:34,020 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:44,283 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:54,545 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:04,805 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:15,062 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:25,319 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:35,581 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:45,842 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:56,102 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:06,367 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:16,628 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:26,893 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:37,161 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:47,426 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:57,688 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:01,705 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:07,952 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:18,218 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:28,480 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:38,742 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:49,008 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:59,276 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:09,540 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:19,804 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:30,070 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:40,333 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:50,601 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 12:00:02,060 - [BITVAVO] - root - WARNING - Currency EUR not found in balance.
2025-07-24 12:00:03,055 - [BITVAVO] - root - WARNING - Currency EUR not found in balance.
2025-07-24 12:00:03,055 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-24 12:00:03,055 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-24 12:00:03,055 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-24 12:00:03,055 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-24 12:00:03,055 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-24 12:00:03,154 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-24 12:00:03,154 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753358396005, 'datetime': '2025-07-24T11:59:56.005Z', 'high': 3139.9, 'low': 2980.8, 'bid': 3110.0, 'bidVolume': 4.49069887, 'ask': 3110.2, 'askVolume': 2.15, 'vwap': 3062.6307739738063, 'open': 3132.6, 'close': 3110.0, 'last': 3110.0, 'previousClose': None, 'change': -22.6, 'percentage': -0.7214454446785417, 'average': 3121.3, 'baseVolume': 21001.11012764, 'quoteVolume': 64318646.16452324, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753271996005', 'timestamp': '1753358396005', 'open': '3132.6', 'openTimestamp': '1753272004940', 'high': '3139.9', 'low': '2980.8', 'last': '3.11E+3', 'closeTimestamp': '1753358389147', 'bid': '3110', 'bidSize': '4.49069887', 'ask': '3110.200', 'askSize': '2.15000000', 'volume': '21001.11012764', 'volumeQuote': '64318646.164523239'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 12:00:03,154 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3110.0
2025-07-24 12:00:03,155 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Starting get_current_price
2025-07-24 12:00:03,155 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Exchange ID: bitvavo
2025-07-24 12:00:03,155 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Using existing exchange instance
2025-07-24 12:00:03,155 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Symbol found in exchange markets
2025-07-24 12:00:03,155 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Attempting to fetch ticker...
2025-07-24 12:00:03,207 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker fetched successfully
2025-07-24 12:00:03,207 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker data: {'symbol': 'DOGE/EUR', 'timestamp': 1753358401018, 'datetime': '2025-07-24T12:00:01.018Z', 'high': 0.21849, 'low': 0.18859, 'bid': 0.20305, 'bidVolume': 4191.0, 'ask': 0.20309, 'askVolume': 11878.26176201, 'vwap': 0.203339984416121, 'open': 0.21812, 'close': 0.20385, 'last': 0.20385, 'previousClose': None, 'change': -0.01427, 'percentage': -6.542270309921144, 'average': 0.210985, 'baseVolume': 48279717.86196103, 'quoteVolume': 9817197.077665875, 'info': {'market': 'DOGE-EUR', 'startTimestamp': '1753272001018', 'timestamp': '1753358401018', 'open': '0.21812', 'openTimestamp': '1753272042026', 'high': '0.21849', 'low': '0.18859', 'last': '0.20385', 'closeTimestamp': '1753358210959', 'bid': '0.2030500', 'bidSize': '4191', 'ask': '0.2030900', 'askSize': '11878.26176201', 'volume': '48279717.86196103', 'volumeQuote': '9817197.0776658748229'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 12:00:03,208 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Last price: 0.20385
2025-07-24 12:00:03,301 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-24 12:00:03,304 - [BITVAVO] - root - INFO - Status update sent
