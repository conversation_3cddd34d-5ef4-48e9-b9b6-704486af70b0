2025-07-24 00:02:54,891 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 284 points
2025-07-24 00:02:54,892 - [KRAKEN] - root - INFO - ETH/USDT B&H total return: 36.34%
2025-07-24 00:02:54,895 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 284 points
2025-07-24 00:02:54,896 - [KRAKEN] - root - INFO - BTC/USDT B&H total return: 21.89%
2025-07-24 00:02:54,899 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 284 points
2025-07-24 00:02:54,900 - [KRAKEN] - root - INFO - SOL/USDT B&H total return: -5.52%
2025-07-24 00:02:54,903 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 284 points
2025-07-24 00:02:54,903 - [KRAKEN] - root - INFO - SUI/USDT B&H total return: 15.61%
2025-07-24 00:02:54,906 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 284 points
2025-07-24 00:02:54,907 - [KRAKEN] - root - INFO - XRP/USDT B&H total return: 31.36%
2025-07-24 00:02:54,909 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 284 points
2025-07-24 00:02:54,909 - [KRAKEN] - root - INFO - AAVE/USDT B&H total return: 14.96%
2025-07-24 00:02:54,912 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 284 points
2025-07-24 00:02:54,913 - [KRAKEN] - root - INFO - AVAX/USDT B&H total return: -6.70%
2025-07-24 00:02:54,915 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 284 points
2025-07-24 00:02:54,916 - [KRAKEN] - root - INFO - ADA/USDT B&H total return: 14.59%
2025-07-24 00:02:54,918 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 284 points
2025-07-24 00:02:54,919 - [KRAKEN] - root - INFO - LINK/USDT B&H total return: -3.35%
2025-07-24 00:02:54,921 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 284 points
2025-07-24 00:02:54,922 - [KRAKEN] - root - INFO - TRX/USDT B&H total return: 25.71%
2025-07-24 00:02:54,924 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 284 points
2025-07-24 00:02:54,924 - [KRAKEN] - root - INFO - PEPE/USDT B&H total return: 32.74%
2025-07-24 00:02:54,927 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 284 points
2025-07-24 00:02:54,927 - [KRAKEN] - root - INFO - DOGE/USDT B&H total return: -5.71%
2025-07-24 00:02:54,930 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 284 points
2025-07-24 00:02:54,930 - [KRAKEN] - root - INFO - BNB/USDT B&H total return: 25.55%
2025-07-24 00:02:54,933 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 284 points
2025-07-24 00:02:54,933 - [KRAKEN] - root - INFO - DOT/USDT B&H total return: -15.11%
2025-07-24 00:02:54,934 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-24 00:02:54,948 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-24 00:02:55,065 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-24 00:02:55,078 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-24 00:02:55,881 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:02:57,462 - [KRAKEN] - root - INFO - Added ETH/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,462 - [KRAKEN] - root - INFO - Added BTC/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,462 - [KRAKEN] - root - INFO - Added SOL/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,462 - [KRAKEN] - root - INFO - Added SUI/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,462 - [KRAKEN] - root - INFO - Added XRP/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added AAVE/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added AVAX/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added ADA/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added LINK/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added TRX/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added PEPE/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added DOGE/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added BNB/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added DOT/USDT buy-and-hold curve with 284 points
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-24 00:02:57,463 - [KRAKEN] - root - INFO -   - ETH/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,464 - [KRAKEN] - root - INFO -   - BTC/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,464 - [KRAKEN] - root - INFO -   - SOL/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,464 - [KRAKEN] - root - INFO -   - SUI/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,464 - [KRAKEN] - root - INFO -   - XRP/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,464 - [KRAKEN] - root - INFO -   - AAVE/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,464 - [KRAKEN] - root - INFO -   - AVAX/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,464 - [KRAKEN] - root - INFO -   - ADA/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,465 - [KRAKEN] - root - INFO -   - LINK/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,465 - [KRAKEN] - root - INFO -   - TRX/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,465 - [KRAKEN] - root - INFO -   - PEPE/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,465 - [KRAKEN] - root - INFO -   - DOGE/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,465 - [KRAKEN] - root - INFO -   - BNB/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,466 - [KRAKEN] - root - INFO -   - DOT/USDT: 284 points from 2024-10-13 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,497 - [KRAKEN] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-24 00:02:57,497 - [KRAKEN] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-24 00:02:57,501 - [KRAKEN] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-24 00:02:57,501 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-24 00:02:57,514 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-24 00:02:57,515 - [KRAKEN] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-24 00:02:57,515 - [KRAKEN] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-24 00:02:57,515 - [KRAKEN] - root - INFO - Combination method: consensus
2025-07-24 00:02:57,515 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-24 00:02:57,515 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-24 00:02:57,533 - [KRAKEN] - root - INFO - Loaded 2165 rows of BTC/USDT data from cache (last updated: 2025-07-24)
2025-07-24 00:02:57,534 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-24
2025-07-24 00:02:57,534 - [KRAKEN] - root - INFO - Loaded 2165 rows of BTC/USDT data from cache (after filtering).
2025-07-24 00:02:57,534 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-24 00:02:57,535 - [KRAKEN] - root - INFO - Fetched BTC data: 2165 candles from 2019-08-20 00:00:00+00:00 to 2025-07-23 00:00:00+00:00
2025-07-24 00:02:57,535 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-24 00:02:57,911 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1135)}
2025-07-24 00:02:57,912 - [KRAKEN] - root - INFO - PGO signal: 1
2025-07-24 00:02:57,912 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-24 00:02:58,040 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1139)}
2025-07-24 00:02:58,040 - [KRAKEN] - root - INFO - Bollinger Bands signal: 1
2025-07-24 00:03:03,601 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-24 00:03:03,601 - [KRAKEN] - root - INFO - DWMA Score signal: 1
2025-07-24 00:03:04,193 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-24 00:03:04,194 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(896)}
2025-07-24 00:03:04,194 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-24 00:03:04,194 - [KRAKEN] - root - INFO - DEMA Super Score signal: 1
2025-07-24 00:03:05,721 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-24 00:03:05,722 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(991)}
2025-07-24 00:03:05,722 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-24 00:03:05,722 - [KRAKEN] - root - INFO - DPSD Score signal: 1
2025-07-24 00:03:05,818 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-24 00:03:05,818 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-24 00:03:05,818 - [KRAKEN] - root - INFO - AAD Score signal: 1
2025-07-24 00:03:06,156 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:06,669 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-24 00:03:06,669 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-24 00:03:06,669 - [KRAKEN] - root - INFO - Dynamic EMA Score signal: 1
2025-07-24 00:03:08,097 - [KRAKEN] - root - INFO - Quantile DEMA Score signal: 1
2025-07-24 00:03:08,097 - [KRAKEN] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-24 00:03:08,097 - [KRAKEN] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-24 00:03:08,097 - [KRAKEN] - root - INFO - MTPI Score: 1.000000
2025-07-24 00:03:08,098 - [KRAKEN] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-24 00:03:08,101 - [KRAKEN] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-24 00:03:08,102 - [KRAKEN] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 3.0}
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 12.0, 'BTC/USDT': 0.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 10.0, 'LINK/USDT': 9.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 13.0, 'BNB/USDT': 3.0, 'DOT/USDT': 3.0}
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 12.0)
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 0.0)
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 5.0)
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 5.0)
2025-07-24 00:03:08,102 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 11.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 0.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 7.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 10.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 9.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 5.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 13.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 3.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 3.0)
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:03:08,103 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:03:08,103 - [KRAKEN] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:03:08,120 - [KRAKEN] - root - INFO - Appended metrics to existing file: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250717_run_20250721_225520.csv
2025-07-24 00:03:08,120 - [KRAKEN] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250717_run_20250721_225520.csv
2025-07-24 00:03:08,120 - [KRAKEN] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-24 00:03:08,120 - [KRAKEN] - root - INFO - Results type: <class 'dict'>
2025-07-24 00:03:08,120 - [KRAKEN] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO - Success flag set to: True
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 284 entries
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 284 entries
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 284 entries
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 284 entries
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - metrics_file: <class 'str'>
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - success: <class 'bool'>
2025-07-24 00:03:08,121 - [KRAKEN] - root - INFO -   - message: <class 'str'>
2025-07-24 00:03:08,121 - [KRAKEN] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-24 00:03:08,122 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: XRP/EUR, DOGE/EUR
2025-07-24 00:03:08,122 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-19 00:00:00+00:00     ETH/EUR, XRP/EUR
2025-07-20 00:00:00+00:00     ETH/EUR, XRP/EUR
2025-07-21 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-22 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-23 00:00:00+00:00    XRP/EUR, DOGE/EUR
dtype: object
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:03:08,123 - [KRAKEN] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: incumbent
2025-07-24 00:03:08,123 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-24 00:03:08,123 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:03:08,123 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:03:08,123 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-24 00:03:08,123 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['DOGE/EUR']
2025-07-24 00:03:08,123 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: DOGE/EUR
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] SELECTED BEST ASSET: DOGE/EUR (score: 13.0)
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: DOGE/EUR (MTPI signal: 1)
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['DOGE/EUR']
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: XRP/EUR, DOGE/EUR -> DOGE/EUR
2025-07-24 00:03:08,123 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - Single winner: DOGE/EUR
2025-07-24 00:03:08,154 - [KRAKEN] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-24 00:03:08,154 - [KRAKEN] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:03:08,154 - [KRAKEN] - root - INFO - Available assets from config: ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:03:08,154 - [KRAKEN] - root - INFO - Asset columns found in dataframe: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - Assets with non-zero allocation: ['XRP/EUR', 'DOGE/EUR']
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - All assets sorted by score: [('DOGE/EUR', 13.0), ('ETH/EUR', 12.0), ('XRP/EUR', 11.0), ('ADA/EUR', 10.0), ('LINK/EUR', 9.0), ('AVAX/EUR', 7.0), ('SOL/EUR', 5.0), ('SUI/EUR', 5.0), ('PEPE/EUR', 5.0), ('BNB/EUR', 3.0), ('DOT/EUR', 3.0), ('TRX/EUR', 1.0), ('BTC/EUR', 0.0), ('AAVE/EUR', 0.0)]
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - Selected top 2 assets by score: ['DOGE/EUR', 'ETH/EUR']
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - Updated assets list with top-scoring assets: ['DOGE/EUR', 'ETH/EUR']
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - Using weighted allocation with configured weights: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - [DEBUG]   - Best asset selected: DOGE/EUR
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - [DEBUG]   - Assets held: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-24 00:03:08,155 - [KRAKEN] - root - ERROR - 🚨 ? DOGE/EUR WAS SELECTED
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - Executing multi-asset strategy with 2 assets: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:03:08,155 - [KRAKEN] - root - INFO - Passing 14 asset scores to executor for replacement logic
2025-07-24 00:03:08,229 - [KRAKEN] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Starting get_current_price
2025-07-24 00:03:08,229 - [KRAKEN] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Exchange ID: kraken
2025-07-24 00:03:08,229 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Using existing exchange instance
2025-07-24 00:03:08,229 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Symbol found in exchange markets
2025-07-24 00:03:08,229 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Attempting to fetch ticker...
2025-07-24 00:03:08,325 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker fetched successfully
2025-07-24 00:03:08,325 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker data: {'symbol': 'DOGE/EUR', 'timestamp': None, 'datetime': None, 'high': 0.2304279, 'low': 0.196218, 'bid': 0.204, 'bidVolume': 12637.0, 'ask': 0.2040001, 'askVolume': 13270.0, 'vwap': 0.210775808, 'open': 0.2042824, 'close': 0.2043237, 'last': 0.2043237, 'previousClose': None, 'change': 4.13e-05, 'percentage': 0.0202171112146714, 'average': 0.204303, 'baseVolume': 32478441.86092018, 'quoteVolume': 6845669.825816475, 'info': {'a': ['0.204000100', '13270', '13270.000'], 'b': ['0.204000000', '12637', '12637.000'], 'c': ['0.204323700', '38.72675770'], 'v': ['85586.88933548', '32478441.86092018'], 'p': ['0.204000227', '0.210775808'], 't': ['11', '8471'], 'l': ['0.204000000', '0.196218000'], 'h': ['0.204323700', '0.230427900'], 'o': '0.204282400'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:08,325 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Last price: 0.2043237
2025-07-24 00:03:08,325 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-24 00:03:08,326 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: kraken
2025-07-24 00:03:08,326 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-24 00:03:08,326 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-24 00:03:08,326 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-24 00:03:09,285 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-24 00:03:09,286 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': None, 'datetime': None, 'high': 3208.26, 'low': 2995.66, 'bid': 3078.0, 'bidVolume': 4.0, 'ask': 3078.01, 'askVolume': 48.0, 'vwap': 3100.90845, 'open': 3083.14, 'close': 3078.57, 'last': 3078.57, 'previousClose': None, 'change': -4.57, 'percentage': -0.148225510356325, 'average': 3080.85, 'baseVolume': 19017.62758533, 'quoteVolume': 58971922.07830289, 'info': {'a': ['3078.01000', '48', '48.000'], 'b': ['3078.00000', '4', '4.000'], 'c': ['3078.57000', '0.10500000'], 'v': ['5.22014710', '19017.62758533'], 'p': ['3081.07679', '3100.90845'], 't': ['18', '26675'], 'l': ['3078.57000', '2995.66000'], 'h': ['3083.14000', '3208.26000'], 'o': '3083.14000'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:09,286 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3078.57
2025-07-24 00:03:09,286 - [KRAKEN] - root - INFO - Original assets with weights: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:03:09,287 - [KRAKEN] - root - INFO - Using provided asset scores for replacement logic: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:03:09,287 - [KRAKEN] - root - INFO - Top assets by score:
2025-07-24 00:03:09,287 - [KRAKEN] - root - INFO -   DOGE/EUR: score=13.0, daily trade limit: OK
2025-07-24 00:03:09,287 - [KRAKEN] - root - INFO -   ETH/EUR: score=12.0, daily trade limit: OK
2025-07-24 00:03:09,287 - [KRAKEN] - root - INFO -   XRP/EUR: score=11.0, daily trade limit: OK
2025-07-24 00:03:09,288 - [KRAKEN] - root - INFO -   ADA/EUR: score=10.0, daily trade limit: OK
2025-07-24 00:03:09,288 - [KRAKEN] - root - INFO -   LINK/EUR: score=9.0, daily trade limit: OK
2025-07-24 00:03:09,288 - [KRAKEN] - root - INFO -   AVAX/EUR: score=7.0, daily trade limit: OK
2025-07-24 00:03:09,288 - [KRAKEN] - root - INFO -   SOL/EUR: score=5.0, daily trade limit: OK
2025-07-24 00:03:09,288 - [KRAKEN] - root - INFO -   SUI/EUR: score=5.0, daily trade limit: OK
2025-07-24 00:03:09,288 - [KRAKEN] - root - INFO -   PEPE/EUR: score=5.0, daily trade limit: OK
2025-07-24 00:03:09,288 - [KRAKEN] - root - INFO -   BNB/EUR: score=3.0, daily trade limit: OK
2025-07-24 00:03:09,289 - [KRAKEN] - root - INFO - Incremented daily trade counter for ETH/EUR: 1/5
2025-07-24 00:03:09,289 - [KRAKEN] - root - INFO - No assets were rejected during execution
2025-07-24 00:03:09,289 - [KRAKEN] - root - INFO - Attempting to exit position for XRP/EUR in live mode
2025-07-24 00:03:09,289 - [KRAKEN] - root - INFO - [DEBUG] PRICE - XRP/EUR: Starting get_current_price
2025-07-24 00:03:09,289 - [KRAKEN] - root - INFO - [DEBUG] PRICE - XRP/EUR: Exchange ID: kraken
2025-07-24 00:03:09,289 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Using existing exchange instance
2025-07-24 00:03:09,289 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Symbol found in exchange markets
2025-07-24 00:03:09,289 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Attempting to fetch ticker...
2025-07-24 00:03:10,289 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Ticker fetched successfully
2025-07-24 00:03:10,289 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Ticker data: {'symbol': 'XRP/EUR', 'timestamp': None, 'datetime': None, 'high': 3.01894, 'low': 2.58872, 'bid': 2.69484, 'bidVolume': 102.0, 'ask': 2.69501, 'askVolume': 1000.0, 'vwap': 2.77681885, 'open': 2.6969, 'close': 2.69953, 'last': 2.69953, 'previousClose': None, 'change': 0.00263, 'percentage': 0.0975193740961845, 'average': 2.69821, 'baseVolume': 11728493.00926603, 'quoteVolume': 32567900.470223136, 'info': {'a': ['2.69501000', '1000', '1000.000'], 'b': ['2.69484000', '102', '102.000'], 'c': ['2.69953000', '16.19196100'], 'v': ['1665.98598471', '11728493.00926603'], 'p': ['2.69983411', '2.77681885'], 't': ['4', '20272'], 'l': ['2.69690000', '2.58872000'], 'h': ['2.69991000', '3.01894000'], 'o': '2.69690000'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:10,290 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - XRP/EUR: Last price: 2.69953
2025-07-24 00:03:16,548 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:20,221 - [KRAKEN] - root - INFO - Current position for XRP/EUR: 0.00000000 units (entry price: 0.00000000)
2025-07-24 00:03:20,222 - [KRAKEN] - root - ERROR - No position found for XRP/EUR
2025-07-24 00:03:20,223 - [KRAKEN] - root - ERROR - Trade failed: SELL XRP/EUR, amount=0.00000000, price=2.69953000, reason=No position found for XRP/EUR
2025-07-24 00:03:20,224 - [KRAKEN] - root - ERROR - Failed to exit position for XRP/EUR: No position found for XRP/EUR
2025-07-24 00:03:26,816 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:35,258 - [KRAKEN] - root - INFO - [DEBUG] PRICE - CVX/EUR: Starting get_current_price
2025-07-24 00:03:35,258 - [KRAKEN] - root - INFO - [DEBUG] PRICE - CVX/EUR: Exchange ID: kraken
2025-07-24 00:03:35,258 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Using existing exchange instance
2025-07-24 00:03:35,258 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Symbol found in exchange markets
2025-07-24 00:03:35,258 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Attempting to fetch ticker...
2025-07-24 00:03:35,345 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Ticker fetched successfully
2025-07-24 00:03:35,345 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Ticker data: {'symbol': 'CVX/EUR', 'timestamp': None, 'datetime': None, 'high': 4.43, 'low': 3.839, 'bid': 4.051, 'bidVolume': 3.0, 'ask': 4.083, 'askVolume': 127.0, 'vwap': 4.1605, 'open': 4.093, 'close': 4.067, 'last': 4.067, 'previousClose': None, 'change': -0.026, 'percentage': -0.6352308819936476, 'average': 4.08, 'baseVolume': 17706.7333859, 'quoteVolume': 73668.86425203695, 'info': {'a': ['4.08300', '127', '127.000'], 'b': ['4.05100', '3', '3.000'], 'c': ['4.06700', '92.33264697'], 'v': ['17706.73338590', '17706.73338590'], 'p': ['4.16050', '4.16050'], 't': ['360', '360'], 'l': ['3.83900', '3.83900'], 'h': ['4.43000', '4.43000'], 'o': '4.09300'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:35,346 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Last price: 4.067
2025-07-24 00:03:35,346 - [KRAKEN] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Starting get_current_price
2025-07-24 00:03:35,346 - [KRAKEN] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Exchange ID: kraken
2025-07-24 00:03:35,346 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Using existing exchange instance
2025-07-24 00:03:35,346 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Symbol found in exchange markets
2025-07-24 00:03:35,346 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Attempting to fetch ticker...
2025-07-24 00:03:36,336 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker fetched successfully
2025-07-24 00:03:36,337 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker data: {'symbol': 'DOGE/EUR', 'timestamp': None, 'datetime': None, 'high': 0.2304279, 'low': 0.196218, 'bid': 0.2039304, 'bidVolume': 80791.0, 'ask': 0.2039305, 'askVolume': 14300.0, 'vwap': 0.210775592, 'open': 0.2042824, 'close': 0.204, 'last': 0.204, 'previousClose': None, 'change': -0.0002824, 'percentage': -0.1382400050126687, 'average': 0.2041412, 'baseVolume': 32479478.14029333, 'quoteVolume': 6845881.232871385, 'info': {'a': ['0.203930500', '14300', '14300.000'], 'b': ['0.203930400', '80791', '80791.000'], 'c': ['0.204000000', '0.00007315'], 'v': ['86623.16870863', '32479478.14029333'], 'p': ['0.204000225', '0.210775592'], 't': ['13', '8473'], 'l': ['0.204000000', '0.196218000'], 'h': ['0.204323700', '0.230427900'], 'o': '0.204282400'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:36,337 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Last price: 0.204
2025-07-24 00:03:36,337 - [KRAKEN] - root - INFO - Total portfolio value (cached): 8728.72784402 EUR
2025-07-24 00:03:36,337 - [KRAKEN] - root - INFO - Using safe available balance: 3962.38892148 (99.99% of 3962.78520000)
2025-07-24 00:03:36,338 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-24 00:03:36,338 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: kraken
2025-07-24 00:03:36,338 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-24 00:03:36,338 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-24 00:03:36,338 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-24 00:03:37,083 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:37,304 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-24 00:03:37,304 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': None, 'datetime': None, 'high': 3208.26, 'low': 2995.66, 'bid': 3074.64, 'bidVolume': 16.0, 'ask': 3074.67, 'askVolume': 1.0, 'vwap': 3100.90359, 'open': 3083.14, 'close': 3075.23, 'last': 3075.23, 'previousClose': None, 'change': -7.91, 'percentage': -0.2565566273344707, 'average': 3079.18, 'baseVolume': 19021.58538559, 'quoteVolume': 58984102.40966757, 'info': {'a': ['3074.67000', '1', '1.000'], 'b': ['3074.64000', '16', '16.000'], 'c': ['3075.23000', '0.00698688'], 'v': ['9.19274842', '19021.58538559'], 'p': ['3079.73288', '3100.90359'], 't': ['30', '26686'], 'l': ['3074.50000', '2995.66000'], 'h': ['3083.14000', '3208.26000'], 'o': '3083.14000'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:37,305 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3075.23
2025-07-24 00:03:37,305 - [KRAKEN] - root - INFO - Asset ETH/EUR: weight=0.5000, price=3075.23000000, required=1979.17364239, amount=0.64102148
2025-07-24 00:03:37,305 - [KRAKEN] - root - INFO - Final assets to buy: {'ETH/EUR'}
2025-07-24 00:03:37,306 - [KRAKEN] - root - INFO - Weight for DOGE/EUR unchanged (current: 0.50, new: 0.50). Skipping adjustment.
2025-07-24 00:03:37,306 - [KRAKEN] - root - INFO - Processing position reductions (sells) first to free up capital...
2025-07-24 00:03:38,224 - [KRAKEN] - root - INFO - Available balance after position reductions: 3962.78520000 EUR
2025-07-24 00:03:38,225 - [KRAKEN] - root - INFO - Processing position increases (buys) after capital has been freed...
2025-07-24 00:03:38,225 - [KRAKEN] - root - INFO - Total sale proceeds to distribute: 0.00000000 EUR
2025-07-24 00:03:38,225 - [KRAKEN] - root - INFO - Total weight of new assets: 0.5000
2025-07-24 00:03:38,225 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-24 00:03:38,226 - [KRAKEN] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: kraken
2025-07-24 00:03:38,226 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-24 00:03:38,226 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-24 00:03:38,226 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-24 00:03:38,290 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-24 00:03:38,290 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': None, 'datetime': None, 'high': 3208.26, 'low': 2995.66, 'bid': 3074.64, 'bidVolume': 16.0, 'ask': 3074.67, 'askVolume': 1.0, 'vwap': 3100.90359, 'open': 3083.14, 'close': 3075.23, 'last': 3075.23, 'previousClose': None, 'change': -7.91, 'percentage': -0.2565566273344707, 'average': 3079.18, 'baseVolume': 19021.58538559, 'quoteVolume': 58984102.40966757, 'info': {'a': ['3074.67000', '1', '1.000'], 'b': ['3074.64000', '16', '16.000'], 'c': ['3075.23000', '0.00698688'], 'v': ['9.19274842', '19021.58538559'], 'p': ['3079.73288', '3100.90359'], 't': ['30', '26686'], 'l': ['3074.50000', '2995.66000'], 'h': ['3083.14000', '3208.26000'], 'o': '3083.14000'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 00:03:38,291 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3075.23
2025-07-24 00:03:38,291 - [KRAKEN] - root - INFO - Using weight-based allocation for ETH/EUR: 1981.19446074 EUR (50.0% of 3962.38892148)
2025-07-24 00:03:38,291 - [KRAKEN] - root - INFO - Buying ETH/EUR: 0.64102148 units at 3075.23000000 (value: 1971.28848844)
2025-07-24 00:03:38,325 - [KRAKEN] - root - INFO - Adjusted base amount for ETH/EUR: 0.64102148 -> 0.63781637
2025-07-24 00:03:38,452 - [KRAKEN] - root - WARNING - CCXT NoneType comparison error for ETH/EUR: '>' not supported between instances of 'NoneType' and 'int'
2025-07-24 00:03:38,452 - [KRAKEN] - root - INFO - This is a known Kraken issue - order likely succeeded despite the error
2025-07-24 00:03:38,452 - [KRAKEN] - root - INFO - Created recovery order object for ETH/EUR - trade likely successful
2025-07-24 00:03:38,453 - [KRAKEN] - root - INFO - Entered position for ETH/EUR: 0.641021 units ($1981.19)
2025-07-24 00:03:38,453 - [KRAKEN] - root - INFO - Updated portfolio with successful trades: {'DOGE/EUR': 0.5, 'ETH/EUR': 0.5}
2025-07-24 00:03:38,453 - [KRAKEN] - root - WARNING - Critical portfolio rebalancing failure detected: 33.3% success rate
2025-07-24 00:03:41,228 - [KRAKEN] - root - ERROR - Trade failed: UNKNOWN XRP/EUR, amount=0.00000000, price=0.00000000, reason=No position found for XRP/EUR
2025-07-24 00:03:41,230 - [KRAKEN] - root - INFO - Trade executed: BUY ETH/EUR, amount=0.64102148, price=3075.23000000, filled=0.64102148
2025-07-24 00:03:41,231 - [KRAKEN] - root - INFO - Multi-asset trade result logged to trade log file
2025-07-24 00:03:41,231 - [KRAKEN] - root - INFO - Multi-asset trades partially executed: 1 of 2 trades successful
2025-07-24 00:03:41,319 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-24 00:03:41,323 - [KRAKEN] - root - WARNING - Some trades failed: 1 trades failed
2025-07-24 00:03:41,324 - [KRAKEN] - root - WARNING - Failed trade for XRP/EUR: No position found for XRP/EUR
2025-07-24 00:03:41,327 - [KRAKEN] - root - INFO - Asset selection logged: 2 assets selected with weighted_custom allocation
2025-07-24 00:03:41,328 - [KRAKEN] - root - INFO - Asset scores (sorted by score):
2025-07-24 00:03:41,328 - [KRAKEN] - root - INFO -   DOGE/EUR: score=13.0, status=SELECTED, weight=0.50
2025-07-24 00:03:41,328 - [KRAKEN] - root - INFO -   ETH/EUR: score=12.0, status=SELECTED, weight=0.50
2025-07-24 00:03:41,328 - [KRAKEN] - root - INFO -   XRP/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,328 - [KRAKEN] - root - INFO -   ADA/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,328 - [KRAKEN] - root - INFO -   LINK/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   AVAX/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   SOL/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   SUI/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   PEPE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   BNB/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   DOT/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   BTC/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,329 - [KRAKEN] - root - INFO -   AAVE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-24 00:03:41,330 - [KRAKEN] - root - INFO - Asset selection logged with 14 assets scored and 2 assets selected
2025-07-24 00:03:41,330 - [KRAKEN] - root - INFO - Extracted asset scores: {'ETH/EUR': 12.0, 'BTC/EUR': 0.0, 'SOL/EUR': 5.0, 'SUI/EUR': 5.0, 'XRP/EUR': 11.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 10.0, 'LINK/EUR': 9.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 5.0, 'DOGE/EUR': 13.0, 'BNB/EUR': 3.0, 'DOT/EUR': 3.0}
2025-07-24 00:03:41,330 - [KRAKEN] - root - INFO - Top 2 assets by score: ['DOGE/EUR', 'ETH/EUR']
2025-07-24 00:03:41,379 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-24 00:03:41,380 - [KRAKEN] - root - INFO - Strategy execution completed successfully in 85.73 seconds
2025-07-24 00:03:41,382 - [KRAKEN] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-24 00:03:41,382 - [KRAKEN] - root - WARNING - Recovery from strategy_execution_failure failure was unsuccessful
2025-07-24 00:03:41,434 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-24 00:03:47,344 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:03:57,606 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:08,317 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:18,582 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:28,849 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:39,115 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:41,436 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:49,376 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:04:59,647 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:09,906 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:20,172 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:30,438 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:40,701 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:05:50,965 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:01,227 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:11,487 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:21,748 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:32,013 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:42,278 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:06:52,541 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:02,808 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:13,072 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:23,335 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:33,599 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:43,865 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:07:54,132 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:04,397 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:14,664 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:24,931 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:35,197 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:45,459 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:08:55,725 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:05,991 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:16,255 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:26,520 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:36,782 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:41,438 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:47,050 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 00:09:57,312 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-24 12:00:47,742 - [KRAKEN] - root - WARNING - Error getting price for ETH.F/EUR: kraken does not have market symbol ETH.F/EUR
2025-07-24 12:00:48,730 - [KRAKEN] - root - WARNING - Error getting price for SOL.F/EUR: kraken does not have market symbol SOL.F/EUR
2025-07-24 12:00:48,830 - [KRAKEN] - root - WARNING - Error getting price for TRX.F/EUR: kraken does not have market symbol TRX.F/EUR
2025-07-24 12:00:53,832 - [KRAKEN] - root - WARNING - Error getting price for ETH.F/EUR: kraken does not have market symbol ETH.F/EUR
2025-07-24 12:00:54,794 - [KRAKEN] - root - WARNING - Error getting price for SOL.F/EUR: kraken does not have market symbol SOL.F/EUR
2025-07-24 12:00:54,895 - [KRAKEN] - root - WARNING - Error getting price for TRX.F/EUR: kraken does not have market symbol TRX.F/EUR
2025-07-24 12:00:59,825 - [KRAKEN] - root - INFO - [DEBUG] PRICE - CVX/EUR: Starting get_current_price
2025-07-24 12:00:59,825 - [KRAKEN] - root - INFO - [DEBUG] PRICE - CVX/EUR: Exchange ID: kraken
2025-07-24 12:00:59,826 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Using existing exchange instance
2025-07-24 12:00:59,826 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Symbol found in exchange markets
2025-07-24 12:00:59,826 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Attempting to fetch ticker...
2025-07-24 12:00:59,925 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Ticker fetched successfully
2025-07-24 12:00:59,925 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Ticker data: {'symbol': 'CVX/EUR', 'timestamp': None, 'datetime': None, 'high': 5.5, 'low': 3.819, 'bid': 4.127, 'bidVolume': 162.0, 'ask': 4.155, 'askVolume': 6.0, 'vwap': 4.13301, 'open': 3.978, 'close': 4.122, 'last': 4.122, 'previousClose': None, 'change': 0.144, 'percentage': 3.6199095022624435, 'average': 4.05, 'baseVolume': 28490.40712174, 'quoteVolume': 117751.13753822264, 'info': {'a': ['4.15500', '6', '6.000'], 'b': ['4.12700', '162', '162.000'], 'c': ['4.12200', '7.27802037'], 'v': ['21379.69999834', '28490.40712174'], 'p': ['4.17420', '4.13301'], 't': ['208', '365'], 'l': ['3.81900', '3.81900'], 'h': ['5.50000', '5.50000'], 'o': '3.97800'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 12:00:59,926 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - CVX/EUR: Last price: 4.122
2025-07-24 12:00:59,926 - [KRAKEN] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Starting get_current_price
2025-07-24 12:00:59,926 - [KRAKEN] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Exchange ID: kraken
2025-07-24 12:00:59,926 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Using existing exchange instance
2025-07-24 12:00:59,926 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Symbol found in exchange markets
2025-07-24 12:00:59,926 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Attempting to fetch ticker...
2025-07-24 12:01:00,862 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker fetched successfully
2025-07-24 12:01:00,863 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker data: {'symbol': 'DOGE/EUR', 'timestamp': None, 'datetime': None, 'high': 0.2185101, 'low': 0.1887517, 'bid': 0.2030833, 'bidVolume': 40345.0, 'ask': 0.2030867, 'askVolume': 70451.0, 'vwap': 0.205002874, 'open': 0.2042824, 'close': 0.2029248, 'last': 0.2029248, 'previousClose': None, 'change': -0.0013576, 'percentage': -0.6645702223980137, 'average': 0.2036036, 'baseVolume': 34969150.77194688, 'quoteVolume': 7168776.409588429, 'info': {'a': ['0.203086700', '70451', '70451.000'], 'b': ['0.203083300', '40345', '40345.000'], 'c': ['0.202924800', '26.83700793'], 'v': ['10657698.51475613', '34969150.77194688'], 'p': ['0.199301440', '0.205002874'], 't': ['4129', '10017'], 'l': ['0.188751700', '0.188751700'], 'h': ['0.209410700', '0.218510100'], 'o': '0.204282400'}, 'indexPrice': None, 'markPrice': None}
2025-07-24 12:01:00,863 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Last price: 0.2029248
2025-07-24 12:01:00,945 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-24 12:01:00,947 - [KRAKEN] - root - INFO - Status update sent
